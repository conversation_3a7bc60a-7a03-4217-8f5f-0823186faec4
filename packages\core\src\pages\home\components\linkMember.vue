<template>
    <div>
        <div class="link-member" @click="showProp">
            <!-- 使用Vant的Swipe组件 -->
            <van-swipe
                class="swipe-container"
                :autoplay="3000"
                :show-indicators="true"
                @change="onChange"
                indicator-color="#7438FF"
            >
                <van-swipe-item>
                    <van-badge content="new" :offset="[-10, 3]">
                        <img :src="$imgs['line-member1.png']" alt="" class="swipe-image" />
                    </van-badge>
                </van-swipe-item>
                <van-swipe-item>
                    <van-badge content="new" :offset="[-10, 3]">
                        <img :src="$imgs['line-member2.png']" alt="" class="swipe-image" />
                    </van-badge>
                </van-swipe-item>
            </van-swipe>

            <!-- 文案 -->
            <div class="member-text">Link著數會員</div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useDialog } from 'hook'
import linkMemberDialog from '@/components/linkMemberDialog.vue'

const dialog = useDialog({
    linkMemberDialog
})

// 显示弹窗
const showProp = () => {
    dialog.get('linkMemberDialog').show({})
}
</script>

<style scoped lang="less">
.link-member {
    position: relative;
    width: 100%;
}

.swipe-container {
    width: 85px;
    height: 85px;
    border-radius: 8px;
    overflow: hidden;
    margin: 0 auto;

    // 自定义指示器样式
    :deep(.van-swipe__indicator) {
        width: 8px;
        height: 8px;
        background-color: #cccccc;
        opacity: 1;

        &.van-swipe__indicator--active {
            background-color: #7438ff;
        }
    }

    // 调整指示器位置
    :deep(.van-swipe__indicators) {
        bottom: 15px;
    }
}

.swipe-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.member-text {
    font-size: 18px;
    color: #830da7;
    height: 32px;
    padding: 0 12px;
    line-height: 32px;
    text-align: center;
    border-radius: 20px;
    margin-top: -12px;
    background: linear-gradient(94deg, #fffdff 0%, #f0dbff 100%);
    box-shadow: 0px 0 16px 2px rgba(2, 172, 159, 0.16), inset 0px -2px 0px 2px rgba(162, 28, 153, 0.3);
}
.link-member {
    position: relative;
    width: 100%;
}

.carousel-container {
    position: relative;
    width: 88px;
    height: 88px;
    overflow: hidden;
    border-radius: 8px;
    margin: 0 auto;
}

.carousel-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease-in-out;
}

.carousel-item {
    flex: 0 0 50%;
    height: 100%;
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #cccccc;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &.active {
        background-color: #7438ff;
    }
}
</style>
