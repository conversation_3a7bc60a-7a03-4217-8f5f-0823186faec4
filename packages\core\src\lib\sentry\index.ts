import * as Sentry from "@sentry/vue"
import { useEnvConfig } from 'hook'
import type { Router } from 'vue-router'
import type { App } from 'vue'

export default (app: App, router: Router) => {
    Sentry.init({
        environment: useEnvConfig().RUN_ENV,
        app,
        dsn: "https://<EMAIL>/3",
        release: 'mylink-forest-' + useEnvConfig().RUN_ENV,
        integrations: [
            new Sentry.BrowserTracing({
                routingInstrumentation: Sentry.vueRouterInstrumentation(router),
            }),
            new Sentry.Replay(),
        ],
        normalizeDepth: 6,

        // Set tracesSampleRate to 1.0 to capture 100%
        // of transactions for performance monitoring.
        // We recommend adjusting this value in production
        tracesSampleRate: 1.0,

        // Set `tracePropagationTargets` to control for which URLs distributed tracing should be enabled
        tracePropagationTargets: ["localhost", useEnvConfig().API_HOST],

        // Capture Replay for 10% of all sessions,
        // plus for 100% of sessions with an error
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
    })
}
