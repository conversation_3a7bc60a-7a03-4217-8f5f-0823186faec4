{"name": "core", "version": "0.0.1", "description": "前端减碳花园", "scripts": {"sonar": "esno ./scripts/sonarqube-scanner-config.ts", "sonar:dev": "cross-env runEnv=develop yarn sonar", "serve": "cross-env runEnv=develop vite", "build:uat": "cross-env runEnv=uat vite build && cross-env runEnv=uat yarn sonar", "build:beta": "cross-env runEnv=beta vite build", "build:pat": "cross-env runEnv=production vite build", "tsc": "vue-tsc --noEmit", "changeJs": "node ./scripts/change.js", "compress": "node scripts/tinypng/index.mjs"}, "dependencies": {"@sentry/vue": "^7.73.0", "@via/mylink-sdk": "^0.8.8-beta.3", "@vueuse/core": "^8.2.6", "axios": "^0.27.2", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.10", "hook": "^1.0.0", "pinia": "^2.0.13", "swiper": "^8.3.1", "vant": "^3.5.2", "vite-plugin-compression": "^0.5.1", "vue": "^3.2.33"}, "devDependencies": {"@sentry/vite-plugin": "^2.8.0", "@types/node": "^17.0.25", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/babel-plugin-jsx": "^1.1.1", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "cross-env": "^7.0.3", "eslint": "^8.13.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.2", "md5": "^2.3.0", "path": "^0.12.7", "postcss-pxtorem": "^6.0.0", "prettier": "^2.6.2", "rollup-plugin-external-globals": "^0.6.1", "typescript": "^4.6.3", "vite": "^2.9.5", "vite-plugin-html": "^3.2.0", "vite-plugin-style-import": "1.4.1", "vue-eslint-parser": "^8.3.0", "esno": "0.17.0", "sonarqube-scanner": "3.0.1", "vue-tsc": "^0.34.7"}}