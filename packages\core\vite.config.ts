import { defineConfig, Plugin } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { useAlias } from './config/useAlias'
import { useEnvConfig } from './config/useEnvConfig'
import { createHtmlPlugin } from 'vite-plugin-html'
import externalGlobals from 'rollup-plugin-external-globals'
import styleImport, { VantResolve } from 'vite-plugin-style-import'
import viteCompression from 'vite-plugin-compression'
import { resolve } from 'path'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import postCssPxToRem from 'postcss-pxtorem'

const envConfig = useEnvConfig()

const alias = [...useAlias()]

const injectData = {
    __vite_env__: JSON.stringify(envConfig),
    injectScriptBeforeBody: '',
    injectScriptAfterVue: '',
    injectScriptGlassBox: ''
}
// const reportURI = {
//     uat: `<script id="_cls_detector" data-clsconfig="reportURI=https://report.chinamobilehk.gbqofs.io/reporting/831b1f3b-fdd6-5ec2-b9fc-e762dda9b370/cls_report" src="https://cdn.gbqofs.com/chinamobilehk/u/detector-dom.min.js"></script>`,
//     beta: `<script id="_cls_detector" data-clsconfig="reportURI=https://report.chinamobilehk.gbqofs.io/reporting/157b7976-d19b-4a4c-99a7-b797c7b88bb0/cls_report" src="https://cdn.gbqofs.com/chinamobilehk/u/detector-dom.min.js"></script>`,
//     production: `<script id="_cls_detector" data-clsconfig="reportURI=https://report.chinamobilehk.gbqofs.io/reporting/157b7976-d19b-4a4c-99a7-b797c7b88bb0/cls_report" src="https://cdn.gbqofs.com/chinamobilehk/u/detector-dom.min.js"></script>`
// }

if (envConfig.RUN_ENV !== 'develop') {
    Object.assign(injectData, {
        injectScriptBeforeBody:
            envConfig.RUN_ENV == 'uat'
                ? `<script id="mylinkSdk" src="${envConfig.MYLINK_SDK_URL}" close-arms="true" ali-log-uid="save-energy-forest" env="${envConfig.LOG_ENV}"></script>`
                : `<script id="mylinkSdk" src="${envConfig.MYLINK_SDK_URL}" ali-arms-pid="${envConfig.ArmasPid}" ali-log-uid="save-energy-forest" env="${envConfig.LOG_ENV}"></script>`,
        injectScriptAfterVue: `<script src="${envConfig.BASE}/js/hook.min.js"></script>`
    })
} else {
    // Object.assign(injectData, {
    //  injectScriptBeforeBody: reportURI.uat
    // })
}

export default defineConfig({
    base: envConfig.BASE,
    plugins: [
        vue(),
        // 给监控平台上报sourcemap
        envConfig.RUN_ENV !== 'develop' &&
            sentryVitePlugin({
                debug: true,
                url: 'https://sentry.via.cool',
                sourcemaps: {
                    assets: ['./dist/assets/*'],
                    ignore: ['node_modules'],
                    deleteFilesAfterUpload: `./dist/**/*.map` // 上传后删除 sourcemap 文件
                },
                release: {
                    name: 'mylink-forest-' + useEnvConfig().RUN_ENV
                },
                org: 'sentry',
                project: 'test',
                authToken:
                    '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'
            }),
        vueJsx(),
        createHtmlPlugin({
            minify: true,
            template: 'index.html',
            inject: {
                data: injectData
            }
        }),
        styleImport({
            resolves: [VantResolve()],
            libs: [
                {
                    libraryName: 'vant',
                    esModule: true,
                    resolveStyle: (name) => `../es/${name}/style`
                }
            ]
        }),
        viteCompression({
            algorithm: 'gzip',
            threshold: 10240,
            verbose: false,
            deleteOriginFile: false
        })
    ],
    resolve: {
        alias
    },
    css: {
        postcss: {
            plugins: [
                {
                    postcssPlugin: 'internal:charset-removal',
                    AtRule: {
                        charset: (atRule) => {
                            if (atRule.name === 'charset') {
                                atRule.remove()
                            }
                        }
                    }
                },
                postCssPxToRem({
                    rootValue: 75,
                    /** 排除掉background-position,否则会引起问题 */
                    propList: ['*'],
                    /** 忽略掉特定的样式 */
                    selectorBlackList: ['.norem', 'norem'],
                    
                })
            ]
        },
        preprocessorOptions: {
            less: {
                javascriptEnabled: true,
                additionalData: `
          @import "${resolve(__dirname, 'src/styles/less/g-mixin.less')}";
        `
            }
        }
    },
    server: {
        port: 3002,
        host: '0.0.0.0'
    },
    build: {
        assetsInlineLimit: 0,
        target: 'es2017',
        sourcemap: true,
        rollupOptions: {
            output: {
                entryFileNames: `assets/[name]-[hash].js`,
                chunkFileNames: `assets/[name]-[hash].js`,
                // assetFileNames: `assets/[path]-[name].[ext]`,
                assetFileNames: (chunkInfo) => {
                    if (chunkInfo.name.includes('assets')) {
                        let path = chunkInfo.name.split('assets')[1]
                        return 'assets' + path
                    } else {
                        return 'assets/[name]-[hash].[ext]'
                    }
                }
            },
            external: ['vue', 'hook', '@via/mylink-sdk'],
            plugins: [
                externalGlobals({
                    'vue': 'Vue',
                    'hook': 'Hook',
                    '@via/mylink-sdk': 'mylinkSdk'
                })
            ]
        }
    },
    optimizeDeps: {
        esbuildOptions: {
            target: 'es2020'
        }
    }
})
