<template>
    <div class="ad">
        <div class="title">Link著數會員 x MyGarden</div>
        <div class="desc">開通Link著數會員，即可享受以下权益</div>
        <div class="list">
            <div class="list-item">
                <div class="list-type">
                    <img :src="$imgs['dialog/linkMember-icon1.png']" />
                    <span>種植積分</span>
                </div>
                <p class="list-desc">每個種植階段積分獎都加贈</p>
                <div class="list-box">
                    <div class="left">
                        <img :src="$imgs['dialog/linkMember-icon-points.png']" />
                        <span>积分</span>
                    </div>
                    <img class="up" :src="$imgs['dialog/linkMember-up.png']" />
                    <div class="right">
                        <div class="bg">
                            <span>加赠了</span>
                            <img :src="$imgs['dialog/linkMember-icon-50.png']" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="list-item">
                <div class="list-type">
                    <img :src="$imgs['dialog/linkMember-icon1.png']" />
                    <span>碳值卡</span>
                </div>
                <!-- prettier-ignore -->
                <p class="list-desc">每月<span>後付客戶</span>限定派發碳值升級</p>
                <div class="list-box">
                    <div class="left">
                        <img :src="$imgs['dialog/linkMember-icon-10g.png']" />
                        <span>10g</span>
                    </div>
                    <img class="up" :src="$imgs['dialog/linkMember-up.png']" />
                    <div class="right">
                        <div class="bg">
                            <span>升级至</span>
                            <img :src="$imgs['dialog/linkMember-icon-30g.png']" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tips">
            每月仲有
            <span>$1000＋</span>
            慳錢著數等你拎！
        </div>
        <div class="btn" @click="emit('close')">
            <span>开通会员</span>
        </div>
    </div>
    <img @click="emit('close')" :src="$imgs['closeIcon.png']" alt="" />
</template>

<script lang="ts" setup>
import { useRouter, useLang } from 'hook'
const { state, lang } = useLang()
// const props = defineProps<{
//     title:string
//     desc:string
// }>()
const emit = defineEmits(['close'])

const { router } = useRouter()
</script>

<style lang="less" scoped>
.ad {
    width: 570px;
    background: #ffffff;
    border-radius: 72px 24px 72px 24px;
    background: #ffffff;
    padding: 48px 40px;
    .title {
        font-weight: 600;
        font-size: 32px;
        color: #4e5b7e;
        line-height: 28px;
        text-align: center;
    }
    .desc {
        text-align: center;
        padding: 24px 0;
        font-weight: 400;
        font-size: 24px;
        color: #6a6a6a;
    }
    .list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .list-item {
            padding: 16px;
            width: 230px;
            background: rgba(177, 129, 255, 0.18);
            border-radius: 16px 16px 16px 16px;
            .list-type {
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                    width: 32px;
                    height: 32px;
                    margin: 0 auto;
                }
                span {
                    flex: 1;
                    padding-left: 2px;
                    display: block;
                    font-weight: 600;
                    font-size: 24px;
                    color: #5a32be;
                }
            }
            .list-desc {
                font-weight: 400;
                font-size: 20px;
                color: #6a6a6a;
                line-height: 30px;
                padding: 8px 0;
                span {
                    color: #ff550a;
                }
            }
            .list-box {
                display: flex;
                align-items: flex-end;
                position: relative;
                justify-content: space-between;
                .left {
                    width: 70px;
                    background: #3ac23d;
                    border-radius: 24px 8px 8px 8px;
                    padding: 6px 0;
                    img {
                        width: 42px;
                        height: auto;
                        margin: 0 auto;
                    }
                    span {
                        text-align: center;
                        display: block;
                        font-weight: 500;
                        font-size: 20px;
                        color: #ffffff;
                    }
                }
                > img.up {
                    height: 46px;
                    width: 36px;
                    margin: auto;
                    position: absolute;
                    bottom: 0;
                    left: 70px;
                }
                .right {
                    width: 96px;
                    border-radius: 24px 12px 8px 8px;
                    background: linear-gradient(180deg, #37c93a 0%, #0cb90d 100%);
                    border-radius: 16px 16px 16px 16px;
                    padding: 8px 6px 16px;
                    .bg {
                        width: 84px;
                        background: #ffffff;
                        border-radius: 20px 10px 8px 8px;
                        span {
                            padding-top: 6px;
                            display: block;
                            text-align: center;
                            font-weight: 500;
                            font-size: 20px;
                            color: #3ac23d;
                        }
                        img {
                            width: 72px;
                            margin: 0 auto;
                        }
                    }
                }
            }
        }
    }
    .tips {
        font-weight: 400;
        font-size: 24px;
        color: #6a6a6a;
        line-height: 40px;
        padding: 24px 0;
        text-align: center;
        span {
            color: #ff550a;
        }
    }
    .btn {
        width: 446px;
        height: 84px;
        line-height: 84px;
        font-weight: 400;
        font-size: 36px;
        margin: 0 auto;
        color: #FFFFFF;
        text-align: center;
        background: linear-gradient( 180deg, #9B7EFE 0%, #7B5AFC 100%);
        border-radius: 48px 48px 48px 48px;
    }
}
img {
    width: 56px;
    height: 56px;
    margin: 58px auto 0;
    display: block;
}
</style>
