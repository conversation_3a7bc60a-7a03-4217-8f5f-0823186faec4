const envConfig: Record<string, Record<string, string>> = {
    develop: {
        // API_HOST: 'https://viatest.mylinkapp.hk/mylink/via/energy',
        API_HOST: 'https://via-k8s-uat.mylinkapp.hk/mylink/via/energy/',
        // API_HOST: 'https://via-gray.mylinkapp.hk/mylink/via2/energy-beta/',
        BASE: './',
        LOG_ENV: 'local',
        DEFAULT_TOKEN: 'DEMO189',//vip
        // DEFAULT_TOKEN: 'DEMO311',
        // DEFAULT_TOKEN: 'DEMO316',
        // DEFAULT_TOKEN: 'DEMO318',
        // DEFAULT_TOKEN: 'DEMO054',
        // DEFAULT_TOKEN: 'DEMO0000010',
        // DEFAULT_TOKEN: 'DEMO1000076',//yumushu
        // DEFAULT_TOKEN: 'DEMO1000075',//mumian
        // DEFAULT_TOKEN: 'DEMO0000076',//mumian
        // DEFAULT_TOKEN: 'DEMO07711121',//mumian
        FRIENDS_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=contact',
        ADD_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=notice',
        MYLINK_SDK_URL:'https://uatoss.mylinkapp.hk/via/lib/mylink-sdk/@0.8.8-beta.3/mylink-sdk.umd.js',
        version: 'v1.0.0-dev.1',
        GETIDURL:'https://via-k8s-uat.mylinkapp.hk/gateway/via-system/public/advertisement/config',
        CALLBACKURL:'https://via-k8s-uat.mylinkapp.hk/gateway/via-system/public/advertisement/callback',
        PLAYURL:'https://via-k8s-uat.mylinkapp.hk/gateway/via-system/public/advertisement/play'
    },
    uat: {
        // API_HOST: 'https://viatest.mylinkapp.hk/mylink/via/energy',
        API_HOST: 'https://via-k8s-uat.mylinkapp.hk/mylink/via/energy/',
        BASE: 'https://uatoss.mylinkapp.hk/via/atv/save-energy-forest',
        LOG_ENV: 'daily',
        FRIENDS_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=contact',
        ADD_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=notice',
        MYLINK_SDK_URL: 'https://uatoss.mylinkapp.hk/via/lib/mylink-sdk/@0.8.8-beta.3/mylink-sdk.umd.js',
        version: 'v1.0.0-uat.12',
        GETIDURL:'https://via-k8s-uat.mylinkapp.hk/gateway/via-system/public/advertisement/config',
        CALLBACKURL:'https://via-k8s-uat.mylinkapp.hk/gateway/via-system/public/advertisement/callback',
        PLAYURL:'https://via-k8s-uat.mylinkapp.hk/gateway/via-system/public/advertisement/play'
    },
    beta: {
        // API_HOST: 'https://mylink.hk.chinamobile.com/mylink/via2/energy-beta',
        API_HOST: 'https://via-gray.mylinkapp.hk/mylink/via2/energy-beta/',
        BASE: 'https://cdn.mylink.hk.chinamobile.com/via/atv/beta/save-energy-forest',
        LOG_ENV: 'gray',
        FRIENDS_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=contact',
        ADD_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=notice',
        MYLINK_SDK_URL: 'https://cdn.mylink.hk.chinamobile.com/via/lib/beta/mylink-sdk/@0.8.8-beta.3/mylink-sdk.umd.js',
        ArmasPid:"jlhn8qavag@df79dc7cc1c2d45",
        GETIDURL:'https://via-gray.mylinkapp.hk/gateway/via-system/public/advertisement/config',
        CALLBACKURL:'https://via-gray.mylinkapp.hk/gateway/via-system/public/advertisement/callback',
        PLAYURL:'https://via-gray.mylinkapp.hk/gateway/via-system/public/advertisement/play'
    },
    production: {
        // API_HOST: 'https://mylink.hk.chinamobile.com/mylink/via2/energy',
        API_HOST: 'https://activity.mylinkapp.hk/mylink/via2/energy/',
        BASE: 'https://cdn.mylink.hk.chinamobile.com/via/atv/save-energy-forest',
        LOG_ENV: 'prod',
        FRIENDS_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=contact',
        ADD_URL:'openhkhshlogin://cmcchkhsh://hshhkmyfriends?path=notice',
        MYLINK_SDK_URL: 'https://cdn.mylink.hk.chinamobile.com/via/lib/mylink-sdk/@0.8.8-beta.3/mylink-sdk.umd.js',
        ArmasPid:"jlhn8qavag@df79dc7cc1c2d45",
        GETIDURL:'https://activity.mylinkapp.hk/gateway/via-system/public/advertisement/config',
        CALLBACKURL:'https://activity.mylinkapp.hk/gateway/via-system/public/advertisement/callback',
        PLAYURL:'https://activity.mylinkapp.hk/gateway/via-system/public/advertisement/play'
    }
}

const env = process.env.runEnv as string
console.log('当前运行环境：', env)

export function useEnvConfig() {
    if (!envConfig[env]) {
        throw '当前运行环境不存在'
    }
    const config = { ...envConfig[env] }
    config.RUN_ENV = env
    const keys = Object.keys(config)
    for (let i = 0; i < keys.length; ++i) {
        let key = `VITE_APP_${keys[i]}`
        process.env[key] = config[keys[i]]
    }
    console.log('当前环境变量：', config)
    return config
}
