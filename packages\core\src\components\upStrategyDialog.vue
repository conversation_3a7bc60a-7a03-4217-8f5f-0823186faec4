<template>
    <div class="upStrategyDialog">
        <div class="title">
            {{ props.title }}
        </div>
        <p v-html="props.detail"></p>
        <div v-if="type === 'recharge'" class="button recharge" @click="recharge">{{ state.ai.去充值 }}</div>
        <!-- TODO:三语；判断是否是会员，按钮文案、按钮class、是否跳转 -->
        <div v-else-if="type === 'linkMember'" class="button buyLinkMember" @click="buyLinkMember">
            成为link着数会员? 知道了
        </div>
        <div v-else class="button" @click="eventBus.emit('closeUpStrategyDialog')">{{ state.bearTalk.知道了 }}</div>
    </div>
</template>

<script setup lang="ts">
import { useLang, useEventBus, useEnvConfig } from 'hook'
const { state } = useLang()
const env = useEnvConfig()
const eventBus = useEventBus()
const props = defineProps({
    title: {
        type: String,
        require: true
    },
    detail: {
        type: String,
        require: true
    },
    type: {
        type: String,
        require: true
    }
})

const recharge = () => {
    eventBus.emit('closeUpStrategyDialog')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'http://*************/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    } else {
        location.href =
            'https://mylink.komect.com/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    }
}

const buyLinkMember = () => {
    // TODO: 跳转链接
    eventBus.emit('closeUpStrategyDialog')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'http://*************/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    } else {
        location.href =
            'https://mylink.komect.com/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    }
}
</script>

<style lang="less" scoped>
.upStrategyDialog {
    padding: 40px;
    font-family: PingFang SC, PingFang SC;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
        font-weight: 600;
        font-size: 32px;
        color: #4e5b7e;
        line-height: 40px;
        text-align: center;
        margin-bottom: 32px;
    }

    p {
        font-weight: 400;
        font-size: 28px;
        color: #4e5b7e;
        line-height: 40px;
        text-align: left;
        margin-bottom: 50px;
    }

    .button {
        width: 568px;
        height: 84px;
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 8px 0px 2px #fcaf28, inset 0px 4px 0px 2px #fff2b2;
        border-radius: 48px 12px 48px 12px;
        line-height: 84px;
        font-weight: bold;
        font-size: 36px;
        color: #ffffff;
        text-align: center;
    }
    .recharge {
        background: linear-gradient(180deg, #9f78f5 0%, #7d55f5 100%);
        box-shadow: 0px 8px 0px 2px #6338df, inset 0px 4px 0px 2px #bab2ff;
    }
    .buyLinkMember {
        background: linear-gradient(180deg, #9f78f5 0%, #7d55f5 100%);
        box-shadow: 0px 8px 0px 2px #6338df, inset 0px 4px 0px 2px #bab2ff;
    }
}
</style>
