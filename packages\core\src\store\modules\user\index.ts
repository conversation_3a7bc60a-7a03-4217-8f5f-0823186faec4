import { defineStore } from 'pinia'
import actions from './actions'
import type { userType } from './type'

export const useUserStore = defineStore('user', {
    state: (): userType => ({
        inLogin: false,
        isVip: false,
        isGuild: false,
        isSlash: false,
        frameUrl: '',
        headLogo: 'https://mylink.oss-cn-hongkong.aliyuncs.com/ico/sidebar/img_sidebar_profilephoto_unlogin.png',
        name: '--',
        phone: 'default',
        usingCard: false,
        firstAct:false,
        day_limit:0,
        isHK:0,
        userInfo:{},
        waterNum:0,
        third_id:''
    }),
    actions
})
